<script lang="ts">
	import { getData } from './data.remote';
	import TreeView, { type Item } from './TreeView.svelte';

	let data = await getData();

	let serial: number = 1;

	let plants = new Set(data.map((a) => a.plant || 'Blank'));
	let plants2 = new Map(
		data.map((a) => [
			a.plant || 'Blank',
			{
				id: (++serial).toString(),
				title: a.plant || 'Blank',
				icon: 'plant'
			}
		])
	);

	let facility = new Map(data.map((a) => [a.facility || 'Blank', a.plant || 'Blank']));

	let items: Item[] = Array.from(plants)
		.sort()
		.map((a) => ({
			id: (++serial).toString(),
			title: a || 'Blank',
			icon: 'plant',
			children: Array.from(facility.keys())
				.filter((b) => facility.get(b) === a)
				.sort()
				.map((b) => ({
					id: (++serial).toString(),
					title: b || 'Blank',
					icon: 'facility'
				}))
		}));
</script>

<div>
	<TreeView {items} />
</div>
